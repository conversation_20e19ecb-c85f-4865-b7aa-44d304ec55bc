/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: white;
    overflow: hidden;
}

/* RealWear容器 */
.realwear-container {
    width: 100vw;
    height: 100vh;
    background-color: #2a2a2a;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 窗口标题栏 */
.window-header {
    background-color: #3a3a3a;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    border-bottom: 1px solid #555;
}

.window-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ccc;
    font-size: 14px;
    font-weight: 500;
}

.android-icon {
    width: 20px;
    height: 20px;
}

.window-controls {
    display: flex;
    gap: 5px;
}

.control-btn {
    width: 30px;
    height: 25px;
    background-color: #555;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 14px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.control-btn:hover {
    background-color: #666;
}

.control-btn.close:hover {
    background-color: #e74c3c;
}

/* 主界面 */
.main-interface {
    height: calc(100vh - 40px);
    background-color: #1a1a1a;
    display: flex;
    flex-direction: column;
}

/* 顶部状态栏 */
.status-bar {
    height: 50px;
    background-color: #2a2a2a;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    border-bottom: 1px solid #444;
}

.status-left .demo-title {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
}

.status-center .current-time {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
}

.status-right .step-info {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
}

/* 视频显示区域 */
.video-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px;
    background-color: #1a1a1a;
}

.video-frame {
    width: 100%;
    max-width: 600px;
    height: 400px;
    background-color: #333;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
}

.video-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

/* 底部控制按钮 */
.control-buttons {
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    padding: 20px;
    background-color: #1a1a1a;
}

.control-button {
    padding: 15px 35px;
    font-size: 16px;
    font-weight: bold;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.back-button {
    background-color: #4CAF50;
    color: white;
}

.back-button:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.play-button {
    background-color: #666;
    color: white;
}

.play-button:hover {
    background-color: #777;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 102, 102, 0.3);
}

.delete-button {
    background-color: #f44336;
    color: white;
}

.delete-button:hover {
    background-color: #da190b;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.control-button:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .status-bar {
        padding: 0 15px;
    }
    
    .status-left .demo-title,
    .status-center .current-time,
    .status-right .step-info {
        font-size: 14px;
    }
    
    .control-buttons {
        gap: 20px;
        padding: 15px;
    }
    
    .control-button {
        padding: 12px 25px;
        font-size: 14px;
        min-width: 100px;
    }
    
    .video-container {
        padding: 20px;
    }
    
    .video-frame {
        height: 300px;
    }
}
